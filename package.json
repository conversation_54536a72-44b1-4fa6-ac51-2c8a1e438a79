{"name": "on-target-analysis-for-ynab", "version": "0.1.0", "private": true, "description": "On Target Analysis for YNAB - Budget target alignment dashboard", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "deploy:gcp": "./scripts/deploy-gcp.sh", "deploy:secrets": "./scripts/setup-secrets.sh", "deploy:check": "./scripts/deploy-gcp.sh check", "deploy:build": "./scripts/deploy-gcp.sh build"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash": "^4.17.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.0", "axios": "^1.7.0", "date-fns": "^3.6.0", "lodash": "^4.17.21", "next": "^14.2.0", "next-auth": "^4.24.11", "postcss": "^8.4.0", "posthog-js": "^1.252.1", "posthog-node": "^5.1.0", "react": "^18.3.0", "react-dom": "^18.3.0", "recharts": "^2.12.0", "swr": "^2.2.0", "tailwindcss": "^3.4.0", "typescript": "^5.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0", "prettier": "^3.2.0", "prettier-plugin-tailwindcss": "^0.5.0", "ts-jest": "^29.3.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}