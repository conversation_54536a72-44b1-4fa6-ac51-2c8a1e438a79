# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables - SECURITY CRITICAL
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Sensitive files
*.key
*.p12
secrets/
config/secrets.json

# Logs
logs/
*.log

# Cache and temporary files
.cache/
temp/
tmp/

# IDE and editor files
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
