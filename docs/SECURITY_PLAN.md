# YNAB Off-Target Assignment Analysis - Security Implementation Plan

## Security Overview

This document outlines the comprehensive security strategy for the YNAB Off-Target Assignment Analysis application, focusing on protecting sensitive financial data and ensuring secure API integration.

## Security Principles

### Core Security Principles
1. **Defense in Depth**: Multiple layers of security controls
2. **Least Privilege**: Minimal access rights for all components
3. **Secure by Default**: Security built into the architecture
4. **Data Minimization**: Only collect and store necessary data
5. **Transparency**: Clear security practices and policies

### Threat Model

#### Assets to Protect
- **YNAB Personal Access Tokens**: Primary authentication credentials
- **Budget Data**: Financial information from YNAB API
- **User Preferences**: Application configuration and settings
- **Application Code**: Intellectual property and logic

#### Potential Threats
- **Token Exposure**: Access tokens leaked in code or logs
- **Man-in-the-Middle Attacks**: Intercepted API communications
- **Cross-Site Scripting (XSS)**: Malicious script injection
- **Data Injection**: Malicious data from API responses
- **Local Storage Attacks**: Sensitive data in browser storage
- **Environment Variable Exposure**: Secrets in version control

## Authentication and Authorization

### YNAB API Authentication

#### OAuth 2.0 Configuration Management
```typescript
// Environment variable configuration
interface SecurityConfig {
  NEXT_PUBLIC_YNAB_CLIENT_ID: string; // Required: OAuth Client ID
  NEXTAUTH_SECRET: string; // Required: JWT signing secret
  NEXT_PUBLIC_APP_URL: string; // Required: Application URL for OAuth
  NODE_ENV: 'development' | 'production'; // Environment indicator
  API_BASE_URL: string; // YNAB API base URL
  RATE_LIMIT_WINDOW: number; // Rate limiting window in ms
  CACHE_TTL: number; // Cache time-to-live in seconds
}

// Token validation
class TokenValidator {
  private static readonly TOKEN_PATTERN = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;
  
  static validateFormat(token: string): boolean {
    return this.TOKEN_PATTERN.test(token);
  }
  
  static async validateWithAPI(token: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.ynab.com/v1/user', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}
```

#### Secure Token Storage
- **Environment Variables**: Store tokens in `.env.local` file
- **Runtime Only**: Never persist tokens in browser storage
- **Memory Management**: Clear tokens from memory on app termination
- **No Logging**: Exclude tokens from all logging mechanisms

### Authorization Strategy
- **Read-Only Access**: Use read-only scope when available
- **Minimal Permissions**: Request only necessary API endpoints
- **Session Management**: Implement secure session handling
- **Token Rotation**: Support for token refresh when available

## Data Protection

### Data Classification

#### Sensitive Data (High Protection)
- YNAB Personal Access Tokens
- Budget financial amounts
- Category names and structures
- Account balances and transactions

#### Internal Data (Medium Protection)
- Calculated analysis results
- User preferences and settings
- Cache data and temporary files
- Application logs (sanitized)

#### Public Data (Low Protection)
- Application metadata
- Non-sensitive configuration
- Public documentation
- Error messages (generic)

### Data Handling Policies

#### Data Collection
```typescript
interface DataCollectionPolicy {
  // Only collect data necessary for analysis
  collectMinimalData: boolean;
  // No personal identification beyond YNAB data
  noPersonalIdentifiers: boolean;
  // Clear data retention policies
  dataRetentionDays: number;
  // Explicit user consent for data processing
  requireUserConsent: boolean;
}

const DATA_POLICY: DataCollectionPolicy = {
  collectMinimalData: true,
  noPersonalIdentifiers: true,
  dataRetentionDays: 0, // No persistent storage
  requireUserConsent: true
};
```

#### Data Storage
- **No Persistent Storage**: All data processed in memory only
- **Temporary Caching**: Short-lived cache with automatic expiration
- **No Browser Storage**: No localStorage or sessionStorage for sensitive data
- **Memory Cleanup**: Explicit memory cleanup on component unmount

#### Data Transmission
- **HTTPS Only**: All API communications over TLS 1.2+
- **Certificate Pinning**: Validate YNAB API certificates
- **Request Validation**: Validate all outgoing requests
- **Response Sanitization**: Sanitize all incoming data

## Application Security

### Input Validation and Sanitization

```typescript
class InputValidator {
  // Validate YNAB API token format
  static validateToken(token: string): ValidationResult {
    if (!token || typeof token !== 'string') {
      return { valid: false, error: 'Token is required' };
    }
    
    if (!TokenValidator.validateFormat(token)) {
      return { valid: false, error: 'Invalid token format' };
    }
    
    return { valid: true };
  }
  
  // Validate date inputs
  static validateDate(date: string): ValidationResult {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return { valid: false, error: 'Invalid date format' };
    }
    
    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) {
      return { valid: false, error: 'Invalid date value' };
    }
    
    return { valid: true };
  }
  
  // Sanitize API response data
  static sanitizeAPIResponse(data: any): any {
    // Remove potentially dangerous properties
    const sanitized = { ...data };
    delete sanitized.__proto__;
    delete sanitized.constructor;
    
    // Validate numeric values
    if (typeof sanitized.budgeted === 'number') {
      sanitized.budgeted = Math.round(sanitized.budgeted);
    }
    
    return sanitized;
  }
}
```

### Cross-Site Scripting (XSS) Prevention

#### Content Security Policy
```typescript
// Next.js security headers configuration
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline'", // Next.js requirements
      "style-src 'self' 'unsafe-inline'", // Tailwind CSS requirements
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self' https://api.ynab.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ')
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'strict-origin-when-cross-origin'
  },
  {
    key: 'Permissions-Policy',
    value: 'camera=(), microphone=(), geolocation=()'
  }
];
```

#### Output Encoding
- **React JSX**: Automatic XSS protection through React
- **Data Sanitization**: Sanitize all dynamic content
- **HTML Encoding**: Encode special characters in user data
- **URL Validation**: Validate and sanitize URLs

### Error Handling and Logging

#### Secure Error Handling
```typescript
class SecureErrorHandler {
  // Generic error messages for users
  static getUserFriendlyMessage(error: Error): string {
    const errorMap: Record<string, string> = {
      'AUTHENTICATION_FAILED': 'Please check your YNAB access token',
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait and try again',
      'NETWORK_ERROR': 'Unable to connect to YNAB. Please check your internet connection',
      'DATA_PROCESSING_ERROR': 'Error processing budget data',
      'UNKNOWN_ERROR': 'An unexpected error occurred'
    };
    
    return errorMap[error.name] || errorMap['UNKNOWN_ERROR'];
  }
  
  // Sanitized logging (no sensitive data)
  static logError(error: Error, context: string): void {
    const sanitizedError = {
      name: error.name,
      message: this.sanitizeErrorMessage(error.message),
      context,
      timestamp: new Date().toISOString(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server'
    };
    
    console.error('Application Error:', sanitizedError);
  }
  
  private static sanitizeErrorMessage(message: string): string {
    // Remove potential tokens or sensitive data from error messages
    return message
      .replace(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/gi, '[TOKEN]')
      .replace(/Bearer\s+[^\s]+/gi, 'Bearer [TOKEN]')
      .replace(/\$[\d,]+\.?\d*/g, '$[AMOUNT]');
  }
}
```

## Environment and Configuration Security

### Environment Variable Management

#### Development Environment
```bash
# .env.local (never commit to version control)
NEXT_PUBLIC_YNAB_CLIENT_ID=your-oauth-client-id-here
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=YNAB Off-Target Analysis
NEXT_PUBLIC_API_BASE_URL=https://api.ynab.com/v1

# Optional configuration
NEXT_PUBLIC_ENABLE_DEBUG=false
```

#### .gitignore Configuration
```gitignore
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Sensitive files
*.key
*.pem
*.p12
secrets/
config/secrets.json

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cache and temporary files
.next/
.cache/
temp/
tmp/

# IDE and editor files
.vscode/settings.json
.idea/
*.swp
*.swo
*~
```

### Configuration Validation

```typescript
class ConfigValidator {
  static validateEnvironment(): ValidationResult {
    const requiredVars = [
      'NEXT_PUBLIC_YNAB_CLIENT_ID',
      'NEXT_PUBLIC_APP_URL'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
      return {
        valid: false,
        error: `Missing required environment variables: ${missing.join(', ')}`
      };
    }

    // Validate client ID format
    const clientId = process.env.NEXT_PUBLIC_YNAB_CLIENT_ID!;
    if (!clientId || clientId.length < 10) {
      return {
        valid: false,
        error: 'Invalid NEXT_PUBLIC_YNAB_CLIENT_ID: must be a valid OAuth client ID'
      };
    }

    return { valid: true };
  }
}
```

## API Security

### Rate Limiting Implementation

```typescript
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests = 200; // YNAB API limit
  private readonly windowMs = 60 * 60 * 1000; // 1 hour
  
  canMakeRequest(identifier: string = 'default'): boolean {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // Remove requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }
  
  getTimeUntilReset(identifier: string = 'default'): number {
    const requests = this.requests.get(identifier) || [];
    if (requests.length === 0) return 0;
    
    const oldestRequest = Math.min(...requests);
    const resetTime = oldestRequest + this.windowMs;
    
    return Math.max(0, resetTime - Date.now());
  }
}
```

### Request Security

```typescript
class SecureAPIClient {
  private rateLimiter = new RateLimiter();
  
  async makeRequest(endpoint: string, options: RequestInit = {}): Promise<Response> {
    // Check rate limiting
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilReset();
      throw new Error(`RATE_LIMIT_EXCEEDED:${waitTime}`);
    }
    
    // Validate endpoint
    if (!this.isValidEndpoint(endpoint)) {
      throw new Error('INVALID_ENDPOINT');
    }
    
    // Secure headers (token provided by OAuth client)
    const secureHeaders = {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json',
      'User-Agent': 'YNAB-Off-Target-Analysis/1.0',
      ...options.headers
    };
    
    // Make request with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout
    
    try {
      const response = await fetch(`https://api.ynab.com/v1${endpoint}`, {
        ...options,
        headers: secureHeaders,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`API_ERROR:${response.status}`);
      }
      
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }
  
  private isValidEndpoint(endpoint: string): boolean {
    const allowedEndpoints = [
      '/user',
      '/budgets',
      '/budgets/[^/]+',
      '/budgets/[^/]+/categories',
      '/budgets/[^/]+/months',
      '/budgets/[^/]+/months/[^/]+/categories'
    ];
    
    return allowedEndpoints.some(pattern => 
      new RegExp(`^${pattern}$`).test(endpoint)
    );
  }
}
```

## Security Monitoring and Incident Response

### Security Monitoring

#### Logging Strategy
- **Security Events**: Authentication failures, rate limit hits
- **Error Tracking**: Application errors and API failures
- **Performance Monitoring**: Response times and resource usage
- **No Sensitive Data**: Sanitized logs only

#### Monitoring Checklist
- [ ] Failed authentication attempts
- [ ] Rate limit violations
- [ ] Unusual API usage patterns
- [ ] Application errors and crashes
- [ ] Performance degradation
- [ ] Security header violations

### Incident Response Plan

#### Security Incident Types
1. **Token Compromise**: YNAB access token exposed or stolen
2. **Data Breach**: Unauthorized access to budget data
3. **Application Vulnerability**: Security flaw discovered
4. **API Abuse**: Excessive or malicious API usage

#### Response Procedures
1. **Immediate Response**
   - Revoke compromised tokens
   - Stop application if necessary
   - Document incident details

2. **Investigation**
   - Analyze logs and error reports
   - Identify scope of impact
   - Determine root cause

3. **Remediation**
   - Fix security vulnerabilities
   - Update security controls
   - Implement additional monitoring

4. **Recovery**
   - Generate new access tokens
   - Restart application services
   - Verify security measures

## Security Testing and Validation

### Security Testing Checklist

#### Authentication Testing
- [ ] Token validation works correctly
- [ ] Invalid tokens are rejected
- [ ] Token format validation prevents injection
- [ ] API authentication failures are handled gracefully

#### Input Validation Testing
- [ ] All user inputs are validated
- [ ] Malicious inputs are rejected
- [ ] SQL injection attempts fail (if applicable)
- [ ] XSS attempts are prevented

#### API Security Testing
- [ ] Rate limiting works correctly
- [ ] HTTPS is enforced
- [ ] Invalid endpoints are rejected
- [ ] Error messages don't leak sensitive information

#### Configuration Security Testing
- [ ] Environment variables are properly secured
- [ ] Sensitive files are not committed to version control
- [ ] Security headers are properly configured
- [ ] Default configurations are secure

This comprehensive security plan ensures that the YNAB Off-Target Assignment Analysis application maintains the highest standards of security while providing a seamless user experience.
