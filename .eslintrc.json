{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "react-hooks/exhaustive-deps": "warn"}, "env": {"browser": true, "es2020": true, "node": true}}