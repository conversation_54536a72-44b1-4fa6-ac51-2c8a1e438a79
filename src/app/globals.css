@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  body {
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply focus-visible:ring-ring ring-offset-background inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  /* Card Components */
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-soft;
  }

  .card-header {
    @apply border-b border-gray-200 px-6 py-4;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply rounded-b-lg border-t border-gray-200 bg-gray-50 px-6 py-4;
  }

  /* Form Components */
  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .form-label {
    @apply mb-1 block text-sm font-medium text-gray-700;
  }

  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }

  /* Status Indicators */
  .status-on-target {
    @apply border-success-200 bg-success-100 text-success-800;
  }

  .status-over-target {
    @apply border-danger-200 bg-danger-100 text-danger-800;
  }

  .status-under-target {
    @apply border-warning-200 bg-warning-100 text-warning-800;
  }

  .status-no-target {
    @apply border-gray-200 bg-gray-100 text-gray-800;
  }

  /* Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Utility Classes */
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  /* Focus utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Text utilities */
  .text-gradient {
    background: linear-gradient(
      135deg,
      theme('colors.primary.600'),
      theme('colors.primary.400')
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* PostHog Privacy Classes */
  .ph-no-capture {
    /* Elements with this class will not be recorded in session replay */
  }

  .sensitive {
    /* Elements with this class will be masked in session replay */
  }
}
